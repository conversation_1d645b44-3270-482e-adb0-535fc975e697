"""
Service for handling digital signatures
"""
import base64
import io
import logging
from PIL import Image, ImageDraw, ImageFont
from app.utils.firebase import upload_bytes_to_storage
import uuid
import os

logger = logging.getLogger(__name__)

class SignatureService:
    """Service for handling digital signatures."""
    
    @staticmethod
    def validate_signature(signature_data: str) -> bool:
        """
        Validate a base64 encoded signature image.
        
        Args:
            signature_data: Base64 encoded signature image
            
        Returns:
            bool: True if signature is valid, False otherwise
        """
        try:
            if not signature_data or not isinstance(signature_data, str):
                return False
            
            # Remove data URL prefix if present
            if signature_data.startswith('data:image/'):
                signature_data = signature_data.split(',')[1]
            
            # Decode base64
            image_bytes = base64.b64decode(signature_data)
            
            # Try to open as image
            image = Image.open(io.BytesIO(image_bytes))
            
            # Check if image has reasonable dimensions
            width, height = image.size
            if width < 50 or height < 20 or width > 2000 or height > 1000:
                return False
            
            # Check if image is not completely transparent/empty
            if image.mode == 'RGBA':
                # Check if there are any non-transparent pixels
                pixels = list(image.getdata())
                non_transparent = [p for p in pixels if len(p) >= 4 and p[3] > 0]
                if len(non_transparent) < 10:  # At least 10 non-transparent pixels
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Signature validation failed: {str(e)}")
            return False
    
    @staticmethod
    def save_signature(signature_data: str, customer_id: int, document_type: str = 'signature') -> tuple:
        """
        Save a signature image to Firebase Storage.
        
        Args:
            signature_data: Base64 encoded signature image
            customer_id: ID of the customer
            document_type: Type of document (default: 'signature')
            
        Returns:
            tuple: (file_url, storage_path)
        """
        try:
            # Remove data URL prefix if present
            if signature_data.startswith('data:image/'):
                signature_data = signature_data.split(',')[1]
            
            # Decode base64
            image_bytes = base64.b64decode(signature_data)
            
            # Process the image to ensure it's in PNG format
            image = Image.open(io.BytesIO(image_bytes))
            
            # Convert to RGBA if not already
            if image.mode != 'RGBA':
                image = image.convert('RGBA')
            
            # Save as PNG
            output_buffer = io.BytesIO()
            image.save(output_buffer, format='PNG', optimize=True)
            processed_image_bytes = output_buffer.getvalue()
            
            # Create destination path
            filename = f"signature_{uuid.uuid4()}.png"
            destination_path = f"signatures/{customer_id}/{document_type}/{filename}"
            
            # Upload to Firebase Storage
            file_url, storage_path = upload_bytes_to_storage(
                processed_image_bytes,
                destination_path,
                content_type="image/png"
            )
            
            logger.info(f"Signature saved successfully: {storage_path}")
            return file_url, storage_path
            
        except Exception as e:
            logger.error(f"Failed to save signature: {str(e)}")
            raise Exception(f"Failed to save signature: {str(e)}")
    
    @staticmethod
    def create_signature_placeholder(width: int = 300, height: int = 100) -> str:
        """
        Create a placeholder image for missing signatures.
        
        Args:
            width: Width of the placeholder image
            height: Height of the placeholder image
            
        Returns:
            str: Base64 encoded placeholder image
        """
        try:
            # Create a blank image with white background
            image = Image.new('RGBA', (width, height), (255, 255, 255, 255))
            draw = ImageDraw.Draw(image)
            
            # Draw a border
            draw.rectangle([0, 0, width-1, height-1], outline=(200, 200, 200, 255), width=2)
            
            # Add placeholder text
            try:
                # Try to use a default font
                font = ImageFont.load_default()
            except:
                font = None
            
            text = "Handtekening"
            if font:
                # Get text bounding box
                bbox = draw.textbbox((0, 0), text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                # Center the text
                x = (width - text_width) // 2
                y = (height - text_height) // 2
                
                draw.text((x, y), text, fill=(150, 150, 150, 255), font=font)
            else:
                # Fallback without font
                draw.text((width//2 - 40, height//2 - 10), text, fill=(150, 150, 150, 255))
            
            # Convert to base64
            output_buffer = io.BytesIO()
            image.save(output_buffer, format='PNG')
            image_bytes = output_buffer.getvalue()
            
            return base64.b64encode(image_bytes).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Failed to create signature placeholder: {str(e)}")
            raise Exception(f"Failed to create signature placeholder: {str(e)}")
    
    @staticmethod
    def process_signature_for_docx(signature_data: str, max_width: int = 200, max_height: int = 80) -> bytes:
        """
        Process a signature image for embedding in DOCX files.
        
        Args:
            signature_data: Base64 encoded signature image
            max_width: Maximum width for the signature in the document
            max_height: Maximum height for the signature in the document
            
        Returns:
            bytes: Processed image bytes ready for DOCX embedding
        """
        try:
            # Remove data URL prefix if present
            if signature_data.startswith('data:image/'):
                signature_data = signature_data.split(',')[1]
            
            # Decode base64
            image_bytes = base64.b64decode(signature_data)
            image = Image.open(io.BytesIO(image_bytes))
            
            # Convert to RGBA if not already
            if image.mode != 'RGBA':
                image = image.convert('RGBA')
            
            # Resize while maintaining aspect ratio
            original_width, original_height = image.size
            aspect_ratio = original_width / original_height
            
            if original_width > max_width or original_height > max_height:
                if aspect_ratio > 1:  # Wider than tall
                    new_width = max_width
                    new_height = int(max_width / aspect_ratio)
                else:  # Taller than wide
                    new_height = max_height
                    new_width = int(max_height * aspect_ratio)
                
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Save as PNG for DOCX
            output_buffer = io.BytesIO()
            image.save(output_buffer, format='PNG', optimize=True)
            
            return output_buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Failed to process signature for DOCX: {str(e)}")
            raise Exception(f"Failed to process signature for DOCX: {str(e)}")
