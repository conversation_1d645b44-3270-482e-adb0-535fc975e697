import React, { useRef, useEffect, useState } from 'react';
import SignatureCanvas from 'react-signature-canvas';
import { FaEraser, FaSave, FaTimes } from 'react-icons/fa';

interface SignatureCanvasProps {
  onSave: (signatureData: string) => void;
  onCancel: () => void;
  title?: string;
  width?: number;
  height?: number;
}

const SignatureCanvasComponent: React.FC<SignatureCanvasProps> = ({
  onSave,
  onCancel,
  title = "Handtekening",
  width = 400,
  height = 200
}) => {
  const sigCanvas = useRef<SignatureCanvas>(null);
  const [isEmpty, setIsEmpty] = useState(true);

  useEffect(() => {
    // Check if canvas is empty on mount
    checkIfEmpty();
  }, []);

  const checkIfEmpty = () => {
    if (sigCanvas.current) {
      setIsEmpty(sigCanvas.current.isEmpty());
    }
  };

  const handleClear = () => {
    if (sigCanvas.current) {
      sigCanvas.current.clear();
      setIsEmpty(true);
    }
  };

  const handleSave = () => {
    if (sigCanvas.current && !sigCanvas.current.isEmpty()) {
      const signatureData = sigCanvas.current.toDataURL('image/png');
      onSave(signatureData);
    }
  };

  const handleEnd = () => {
    checkIfEmpty();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-dark-surface rounded-lg p-6 max-w-lg w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-amspm-text dark:text-dark-text">
            {title}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <FaTimes size={20} />
          </button>
        </div>

        <div className="mb-4">
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Teken uw handtekening in het onderstaande vak:
          </p>
          
          <div className="border-2 border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
            <SignatureCanvas
              ref={sigCanvas}
              canvasProps={{
                width: width,
                height: height,
                className: 'signature-canvas bg-white'
              }}
              backgroundColor="white"
              penColor="black"
              minWidth={1}
              maxWidth={3}
              onEnd={handleEnd}
            />
          </div>
        </div>

        <div className="flex justify-between items-center">
          <button
            onClick={handleClear}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <FaEraser size={16} />
            Wissen
          </button>

          <div className="flex gap-2">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Annuleren
            </button>
            <button
              onClick={handleSave}
              disabled={isEmpty}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                isEmpty
                  ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                  : 'bg-amspm-blue hover:bg-amspm-blue-dark text-white'
              }`}
            >
              <FaSave size={16} />
              Opslaan
            </button>
          </div>
        </div>

        <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
          Tip: Gebruik een stylus of uw vinger op een touchscreen voor de beste resultaten.
        </div>
      </div>
    </div>
  );
};

export default SignatureCanvasComponent;
