import api from '../api';

export interface SignatureValidationResponse {
  valid: boolean;
  message: string;
}

export interface SignatureSaveResponse {
  message: string;
  file_url: string;
  storage_path: string;
}

export interface SignaturePlaceholderResponse {
  placeholder: string;
  message: string;
}

/**
 * Service for handling digital signatures
 */
export class SignatureService {
  /**
   * Validate a signature image
   * @param signatureData Base64 encoded signature image
   * @returns Promise resolving to validation result
   */
  static async validateSignature(signatureData: string): Promise<SignatureValidationResponse> {
    try {
      const response = await api.post('/api/signatures/validate', {
        signature_data: signatureData
      });
      return response.data;
    } catch (error: any) {
      console.error('Error validating signature:', error);
      throw new Error(error.response?.data?.error || 'Failed to validate signature');
    }
  }

  /**
   * Save a signature image to storage
   * @param signatureData Base64 encoded signature image
   * @param customerId ID of the customer
   * @param documentType Type of document (optional)
   * @returns Promise resolving to save result
   */
  static async saveSignature(
    signatureData: string, 
    customerId: number, 
    documentType: string = 'signature'
  ): Promise<SignatureSaveResponse> {
    try {
      const response = await api.post('/api/signatures/save', {
        signature_data: signatureData,
        customer_id: customerId,
        document_type: documentType
      });
      return response.data;
    } catch (error: any) {
      console.error('Error saving signature:', error);
      throw new Error(error.response?.data?.error || 'Failed to save signature');
    }
  }

  /**
   * Get a placeholder image for missing signatures
   * @returns Promise resolving to placeholder image data
   */
  static async getSignaturePlaceholder(): Promise<SignaturePlaceholderResponse> {
    try {
      const response = await api.get('/api/signatures/placeholder');
      return response.data;
    } catch (error: any) {
      console.error('Error getting signature placeholder:', error);
      throw new Error(error.response?.data?.error || 'Failed to get signature placeholder');
    }
  }

  /**
   * Convert a signature data URL to a blob
   * @param dataUrl Data URL of the signature
   * @returns Blob containing the signature image
   */
  static dataURLToBlob(dataUrl: string): Blob {
    const arr = dataUrl.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    
    return new Blob([u8arr], { type: mime });
  }

  /**
   * Resize a signature image while maintaining aspect ratio
   * @param dataUrl Data URL of the signature
   * @param maxWidth Maximum width
   * @param maxHeight Maximum height
   * @returns Promise resolving to resized image data URL
   */
  static async resizeSignature(
    dataUrl: string, 
    maxWidth: number = 200, 
    maxHeight: number = 80
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }

        // Calculate new dimensions while maintaining aspect ratio
        const aspectRatio = img.width / img.height;
        let newWidth = img.width;
        let newHeight = img.height;

        if (img.width > maxWidth || img.height > maxHeight) {
          if (aspectRatio > 1) { // Wider than tall
            newWidth = maxWidth;
            newHeight = maxWidth / aspectRatio;
          } else { // Taller than wide
            newHeight = maxHeight;
            newWidth = maxHeight * aspectRatio;
          }
        }

        canvas.width = newWidth;
        canvas.height = newHeight;

        // Fill with white background
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, newWidth, newHeight);

        // Draw the resized image
        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        resolve(canvas.toDataURL('image/png'));
      };
      
      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };
      
      img.src = dataUrl;
    });
  }

  /**
   * Check if a signature data URL contains actual signature data
   * @param dataUrl Data URL of the signature
   * @returns Promise resolving to boolean indicating if signature has content
   */
  static async hasSignatureContent(dataUrl: string): Promise<boolean> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          resolve(false);
          return;
        }

        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);

        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;

        // Check for non-white pixels (assuming white background)
        let hasContent = false;
        for (let i = 0; i < data.length; i += 4) {
          const r = data[i];
          const g = data[i + 1];
          const b = data[i + 2];
          const a = data[i + 3];

          // If pixel is not white or transparent, we have content
          if ((r !== 255 || g !== 255 || b !== 255) && a > 0) {
            hasContent = true;
            break;
          }
        }

        resolve(hasContent);
      };
      
      img.onerror = () => {
        resolve(false);
      };
      
      img.src = dataUrl;
    });
  }
}

export default SignatureService;
