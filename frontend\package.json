{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^5.0.1", "@mui/material": "^7.0.0", "@react-oauth/google": "^0.12.1", "@types/file-saver": "^2.0.7", "axios": "^1.8.4", "chart.js": "^4.4.9", "docx": "^9.4.1", "docxtemplater": "^3.61.1", "docxtemplater-image-module": "^3.1.0", "docxtemplater-image-module-free": "^1.1.1", "file-saver": "^2.0.5", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "html-docx-js": "^0.3.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "mammoth": "^1.9.0", "moment": "^2.30.1", "pdf-lib": "^1.17.1", "pizzip": "^3.1.8", "react": "^19.1.0", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "react-modal": "^3.16.3", "react-pdf": "^9.2.1", "react-router": "^7.4.0", "react-router-dom": "^7.5.0", "react-scripts": "^5.0.1", "react-signature-canvas": "^1.0.6", "react-toastify": "^11.0.5", "react-window": "^1.8.11", "socket.io-client": "^4.8.1", "toast": "^0.5.4", "xlsx": "^0.18.5", "yup": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-react": "^7.26.3", "@eslint/js": "^9.21.0", "@types/html-docx-js": "^0.3.4", "@types/react": "^19.0.10", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "^19.0.4", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "babel-loader": "^10.0.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^6.2.4"}}